import { NextApiRequest, NextApiResponse } from 'next';
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import type { CampaignsRow, Database } from '../../src/types';

// Inline utility functions (required for Vercel serverless functions)
async function createSupabaseServerClient(): Promise<SupabaseClient<Database>> {
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;

  if (!supabaseUrl) {
    throw new Error('Missing SUPABASE_URL environment variable.');
  }
  if (!supabaseAnonKey) {
    throw new Error('Missing SUPABASE_ANON_KEY environment variable.');
  }

  return createClient<Database>(supabaseUrl, supabaseAnonKey);
}

function sortCampaignsByFunding(campaigns: CampaignsRow[]): CampaignsRow[] {
  return campaigns.sort((a, b) => {
    // Primary sort: UPD funding amount (highest first)
    const aFunding = a.data.upd_funding_amount || 0;
    const bFunding = b.data.upd_funding_amount || 0;

    if (bFunding !== aFunding) {
      return bFunding - aFunding;
    }

    // Secondary sort: ID (lowest first)
    return a.id - b.id;
  });
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<CampaignsRow[] | { message: string; error?: string }>
) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  try {
    const supabase = await createSupabaseServerClient();

    // Fetch all approved campaigns
    const { data: campaigns, error } = await supabase
      .from('campaigns')
      .select('*')
      .eq('status', 'approved');

    if (error) {
      console.error('Supabase error:', error);
      return res
        .status(500)
        .json({ message: 'Error fetching campaigns', error: error.message });
    }

    if (!campaigns) {
      return res.status(200).json([]);
    }

    const sortedCampaigns = sortCampaignsByFunding(campaigns as CampaignsRow[]);

    res.status(200).json(sortedCampaigns);
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error('API route error:', error);
    res
      .status(500)
      .json({ message: 'Internal Server Error', error: errorMessage });
  }
}
