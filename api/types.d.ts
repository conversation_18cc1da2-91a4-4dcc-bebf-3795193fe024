// Type declarations for Vercel serverless functions
// This provides the Next.js types that Vercel provides at runtime

declare module 'next' {
  import { IncomingMessage, ServerResponse } from 'http';

  export interface NextApiRequest extends IncomingMessage {
    query: { [key: string]: string | string[] };
    method?: string;
    headers: { [key: string]: string | string[] | undefined };
    body: any;
    cookies: { [key: string]: string };
  }

  export interface NextApiResponse<T = any> extends ServerResponse {
    status(statusCode: number): NextApiResponse<T>;
    json(body: T): void;
    send(body: T): void;
    end(data?: any): void;
    setHeader(name: string, value: string | number | readonly string[]): NextApiResponse<T>;
    redirect(statusCode: number, url: string): void;
  }
}
